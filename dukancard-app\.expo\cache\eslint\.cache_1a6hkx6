[{"C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx": "1", "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx": "2", "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx": "3", "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx": "4", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx": "5", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx": "6", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx": "7", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx": "8", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx": "9", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx": "10", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\likes.tsx": "11", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\reviews.tsx": "12", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\subscriptions.tsx": "13", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx": "14", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\edit-profile.tsx": "15", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx": "16", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx": "17", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx": "18", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx": "19", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx": "20", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx": "21", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "22", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx": "23", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\reviews.tsx": "24", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx": "25", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx": "26", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx": "27", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx": "28", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings.tsx": "29", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\subscriptions.tsx": "30", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx": "31", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx": "32", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx": "33", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx": "34", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx": "35", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx": "36", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx": "37", "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx": "38", "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx": "39", "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx": "40", "C:\\web-app\\dukancard-app\\app\\+not-found.tsx": "41", "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx": "42", "C:\\web-app\\dukancard-app\\app\\index.tsx": "43", "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx": "44", "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx": "45", "C:\\web-app\\dukancard-app\\app\\_layout.tsx": "46", "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx": "47", "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx": "48", "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx": "49", "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx": "50", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx": "51", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx": "52", "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx": "53", "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx": "54", "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts": "55", "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx": "56", "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx": "57", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx": "58", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx": "59", "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx": "60", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx": "61", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx": "62", "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx": "63", "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx": "64", "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx": "65", "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx": "66", "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts": "67", "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts": "68", "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts": "69", "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts": "70", "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts": "71", "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts": "72", "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts": "73", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx": "74", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx": "75", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx": "76", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx": "77", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx": "78", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx": "79", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx": "80", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx": "81", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx": "82", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx": "83", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx": "84", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx": "85", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx": "86", "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx": "87", "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx": "88", "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx": "89", "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx": "90", "C:\\web-app\\dukancard-app\\src\\components\\index.ts": "91", "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx": "92", "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx": "93", "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts": "94", "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx": "95", "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx": "96", "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx": "97", "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx": "98", "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx": "99", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx": "100", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx": "101", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx": "102", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx": "103", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx": "104", "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx": "105", "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx": "106", "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx": "107", "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx": "108", "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx": "109", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx": "110", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx": "111", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx": "112", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx": "113", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx": "114", "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx": "115", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx": "116", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx": "117", "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx": "118", "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx": "119", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx": "120", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx": "121", "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx": "122", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx": "123", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx": "124", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx": "125", "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx": "126", "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx": "127", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx": "128", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx": "129", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts": "130", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx": "131", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx": "132", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx": "133", "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx": "134", "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx": "135", "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx": "136", "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx": "137", "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx": "138", "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx": "139", "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx": "140", "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx": "141", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx": "142", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx": "143", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AuthContainer.tsx": "144", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx": "145", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx": "146", "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts": "147", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx": "148", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx": "149", "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx": "150", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx": "151", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx": "152", "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts": "153", "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx": "154", "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts": "155", "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx": "156", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx": "157", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx": "158", "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts": "159", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx": "160", "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts": "161", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx": "162", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx": "163", "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts": "164", "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts": "165", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx": "166", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx": "167", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx": "168", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx": "169", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx": "170", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx": "171", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx": "172", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx": "173", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx": "174", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx": "175", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx": "176", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx": "177", "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts": "178", "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts": "179", "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts": "180", "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx": "181", "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx": "182", "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx": "183", "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx": "184", "C:\\web-app\\dukancard-app\\src\\contexts\\UserDataContext.tsx": "185", "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts": "186", "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts": "187", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts": "188", "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts": "189", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts": "190", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts": "191", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts": "192", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts": "193", "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts": "194", "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts": "195", "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts": "196", "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts": "197", "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts": "198", "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts": "199", "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts": "200", "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts": "201", "C:\\web-app\\dukancard-app\\src\\types\\ad.ts": "202", "C:\\web-app\\dukancard-app\\src\\types\\auth.ts": "203", "C:\\web-app\\dukancard-app\\src\\types\\components.ts": "204", "C:\\web-app\\dukancard-app\\src\\types\\index.ts": "205", "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts": "206", "C:\\web-app\\dukancard-app\\src\\types\\screens.ts": "207", "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts": "208", "C:\\web-app\\dukancard-app\\src\\types\\ui.ts": "209", "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts": "210", "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts": "211", "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts": "212", "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts": "213", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts": "214", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts": "215", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "216", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts": "217", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts": "218", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts": "219", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts": "220", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts": "221", "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts": "222", "C:\\web-app\\dukancard-app\\src\\utils\\index.ts": "223", "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts": "224", "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts": "225", "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts": "226", "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts": "227", "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts": "228", "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts": "229", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\activityService.ts": "230", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\index.ts": "231", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\adService.ts": "232", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\index.ts": "233", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\authService.ts": "234", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\emailOtpService.ts": "235", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\googleAuthService.ts": "236", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\index.ts": "237", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\mobileAuthService.ts": "238", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\nativeGoogleAuth2025.ts": "239", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessCardDataService.ts": "240", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessDiscovery.ts": "241", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessInteractions.ts": "242", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessOnboardingService.ts": "243", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessPostService.ts": "244", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessProfileService.ts": "245", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\index.ts": "246", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\index.ts": "247", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\metricsService.ts": "248", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\offlineService.ts": "249", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\onboardingService.ts": "250", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileCheckService.ts": "251", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileService.ts": "252", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\qrScanService.ts": "253", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\settingsService.ts": "254", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\syncService.ts": "255", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\userRoleStatusService.ts": "256", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\batchProfileService.ts": "257", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerPostService.ts": "258", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerProfileService.ts": "259", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\index.ts": "260", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\index.ts": "261", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\index.ts": "262", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\locationService.ts": "263", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\developmentErrorMonitoring.ts": "264", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\errorTracking.ts": "265", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\index.ts": "266", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\productionErrorLogging.ts": "267", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\index.ts": "268", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postInteractions.ts": "269", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postService.ts": "270", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\socialService.ts": "271", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\unifiedFeedService.ts": "272", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\index.ts": "273", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\productService.ts": "274", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\index.ts": "275", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\realtimeService.ts": "276", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\avatarUploadService.ts": "277", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\businessPostImageUploadService.ts": "278", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\customerPostImageUploadService.ts": "279", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\imageUploadService.ts": "280", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\index.ts": "281", "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\storageService.ts": "282", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressUtils.ts": "283", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressValidation.ts": "284", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\businessSlugValidation.ts": "285", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\index.ts": "286", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\locationUtils.ts": "287", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\profileValidation.ts": "288", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\slugUtils.ts": "289", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\storage-paths.ts": "290", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\supabaseErrorHandler.ts": "291", "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\validation.ts": "292", "C:\\web-app\\dukancard-app\\backend\\types\\activities.ts": "293", "C:\\web-app\\dukancard-app\\backend\\types\\auth.ts": "294", "C:\\web-app\\dukancard-app\\backend\\types\\business.ts": "295", "C:\\web-app\\dukancard-app\\backend\\types\\common.ts": "296", "C:\\web-app\\dukancard-app\\backend\\types\\customer.ts": "297", "C:\\web-app\\dukancard-app\\backend\\types\\index.ts": "298", "C:\\web-app\\dukancard-app\\backend\\types\\posts.ts": "299", "C:\\web-app\\dukancard-app\\backend\\types\\products.ts": "300", "C:\\web-app\\dukancard-app\\backend\\types\\storage.ts": "301", "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx": "302", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx": "303", "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx": "304", "C:\\web-app\\dukancard-app\\src\\components\\ui\\NetworkStatusBanner.tsx": "305", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts": "306", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx": "307", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx": "308", "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts": "309", "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkEmailSection.tsx": "310", "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkPhoneSection.tsx": "311", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordUpdateSection.tsx": "312", "C:\\web-app\\dukancard-app\\src\\components\\useClientOnlyValue.ts": "313"}, {"size": 10581, "mtime": 1751304355132, "results": "314", "hashOfConfig": "315"}, {"size": 33066, "mtime": 1751437289039, "results": "316", "hashOfConfig": "315"}, {"size": 22889, "mtime": 1751388114453, "results": "317", "hashOfConfig": "315"}, {"size": 3486, "mtime": 1751292266305, "results": "318", "hashOfConfig": "315"}, {"size": 2092, "mtime": 1751196935588, "results": "319", "hashOfConfig": "315"}, {"size": 3430, "mtime": 1751196935588, "results": "320", "hashOfConfig": "315"}, {"size": 9212, "mtime": 1751196935588, "results": "321", "hashOfConfig": "315"}, {"size": 2476, "mtime": 1751196935594, "results": "322", "hashOfConfig": "315"}, {"size": 14663, "mtime": 1751196935594, "results": "323", "hashOfConfig": "315"}, {"size": 2427, "mtime": 1751196935594, "results": "324", "hashOfConfig": "315"}, {"size": 7299, "mtime": 1751196935594, "results": "325", "hashOfConfig": "315"}, {"size": 8102, "mtime": 1751196935594, "results": "326", "hashOfConfig": "315"}, {"size": 7380, "mtime": 1751262018661, "results": "327", "hashOfConfig": "315"}, {"size": 5041, "mtime": 1751260878377, "results": "328", "hashOfConfig": "315"}, {"size": 6775, "mtime": 1751226611908, "results": "329", "hashOfConfig": "315"}, {"size": 6821, "mtime": 1751260028210, "results": "330", "hashOfConfig": "315"}, {"size": 9087, "mtime": 1751226387150, "results": "331", "hashOfConfig": "315"}, {"size": 6639, "mtime": 1751262057025, "results": "332", "hashOfConfig": "315"}, {"size": 7545, "mtime": 1751260191225, "results": "333", "hashOfConfig": "315"}, {"size": 6778, "mtime": 1751261637074, "results": "334", "hashOfConfig": "315"}, {"size": 2811, "mtime": 1751452003209, "results": "335", "hashOfConfig": "315"}, {"size": 6273, "mtime": 1751262096143, "results": "336", "hashOfConfig": "315"}, {"size": 17135, "mtime": 1751462886673, "results": "337", "hashOfConfig": "315"}, {"size": 8098, "mtime": 1751196935636, "results": "338", "hashOfConfig": "315"}, {"size": 7803, "mtime": 1751196935651, "results": "339", "hashOfConfig": "315"}, {"size": 2313, "mtime": 1751196935673, "results": "340", "hashOfConfig": "315"}, {"size": 5468, "mtime": 1751196935673, "results": "341", "hashOfConfig": "315"}, {"size": 2707, "mtime": 1751470416284, "results": "342", "hashOfConfig": "315"}, {"size": 3248, "mtime": 1751462880948, "results": "343", "hashOfConfig": "315"}, {"size": 7716, "mtime": 1751291766593, "results": "344", "hashOfConfig": "315"}, {"size": 2426, "mtime": 1751196935695, "results": "345", "hashOfConfig": "315"}, {"size": 659, "mtime": 1751196935700, "results": "346", "hashOfConfig": "315"}, {"size": 20650, "mtime": 1751442297742, "results": "347", "hashOfConfig": "315"}, {"size": 5864, "mtime": 1751430775268, "results": "348", "hashOfConfig": "315"}, {"size": 16496, "mtime": 1751392157784, "results": "349", "hashOfConfig": "315"}, {"size": 16310, "mtime": 1751442504979, "results": "350", "hashOfConfig": "315"}, {"size": 5897, "mtime": 1751224365126, "results": "351", "hashOfConfig": "315"}, {"size": 4763, "mtime": 1751227981990, "results": "352", "hashOfConfig": "315"}, {"size": 4632, "mtime": 1751228035517, "results": "353", "hashOfConfig": "315"}, {"size": 1865, "mtime": 1751470580943, "results": "354", "hashOfConfig": "315"}, {"size": 733, "mtime": 1751227293173, "results": "355", "hashOfConfig": "315"}, {"size": 5334, "mtime": 1751291766606, "results": "356", "hashOfConfig": "315"}, {"size": 992, "mtime": 1751344026984, "results": "357", "hashOfConfig": "315"}, {"size": 2546, "mtime": 1751228167665, "results": "358", "hashOfConfig": "315"}, {"size": 23758, "mtime": 1751228610414, "results": "359", "hashOfConfig": "315"}, {"size": 5093, "mtime": 1751344101856, "results": "360", "hashOfConfig": "315"}, {"size": 6483, "mtime": 1751196935876, "results": "361", "hashOfConfig": "315"}, {"size": 2348, "mtime": 1751389147247, "results": "362", "hashOfConfig": "315"}, {"size": 11982, "mtime": 1751198168666, "results": "363", "hashOfConfig": "315"}, {"size": 7406, "mtime": 1751196935889, "results": "364", "hashOfConfig": "315"}, {"size": 3489, "mtime": 1751196935889, "results": "365", "hashOfConfig": "315"}, {"size": 7684, "mtime": 1751202475903, "results": "366", "hashOfConfig": "315"}, {"size": 9891, "mtime": 1750575923377, "results": "367", "hashOfConfig": "315"}, {"size": 1282, "mtime": 1751198271726, "results": "368", "hashOfConfig": "315"}, {"size": 966, "mtime": 1750614134271, "results": "369", "hashOfConfig": "315"}, {"size": 11914, "mtime": 1751196935903, "results": "370", "hashOfConfig": "315"}, {"size": 10266, "mtime": 1751262337927, "results": "371", "hashOfConfig": "315"}, {"size": 6205, "mtime": 1751198271726, "results": "372", "hashOfConfig": "315"}, {"size": 9873, "mtime": 1751198271726, "results": "373", "hashOfConfig": "315"}, {"size": 6195, "mtime": 1751198271726, "results": "374", "hashOfConfig": "315"}, {"size": 8458, "mtime": 1751202488474, "results": "375", "hashOfConfig": "315"}, {"size": 16128, "mtime": 1751198271726, "results": "376", "hashOfConfig": "315"}, {"size": 2174, "mtime": 1751198271733, "results": "377", "hashOfConfig": "315"}, {"size": 1283, "mtime": 1751196935941, "results": "378", "hashOfConfig": "315"}, {"size": 5954, "mtime": 1751196935943, "results": "379", "hashOfConfig": "315"}, {"size": 737, "mtime": 1750575923371, "results": "380", "hashOfConfig": "315"}, {"size": 196, "mtime": 1751197002631, "results": "381", "hashOfConfig": "315"}, {"size": 163, "mtime": 1751197009882, "results": "382", "hashOfConfig": "315"}, {"size": 196, "mtime": 1751197016903, "results": "383", "hashOfConfig": "315"}, {"size": 394, "mtime": 1751196130079, "results": "384", "hashOfConfig": "315"}, {"size": 237, "mtime": 1751197673887, "results": "385", "hashOfConfig": "315"}, {"size": 218, "mtime": 1751197688254, "results": "386", "hashOfConfig": "315"}, {"size": 211, "mtime": 1751197701118, "results": "387", "hashOfConfig": "315"}, {"size": 3552, "mtime": 1751196935943, "results": "388", "hashOfConfig": "315"}, {"size": 16310, "mtime": 1751202047935, "results": "389", "hashOfConfig": "315"}, {"size": 17318, "mtime": 1751196935955, "results": "390", "hashOfConfig": "315"}, {"size": 3165, "mtime": 1751196935955, "results": "391", "hashOfConfig": "315"}, {"size": 11504, "mtime": 1751202056440, "results": "392", "hashOfConfig": "315"}, {"size": 12748, "mtime": 1751196935969, "results": "393", "hashOfConfig": "315"}, {"size": 4745, "mtime": 1751196935969, "results": "394", "hashOfConfig": "315"}, {"size": 853, "mtime": 1750575923388, "results": "395", "hashOfConfig": "315"}, {"size": 24364, "mtime": 1751259547993, "results": "396", "hashOfConfig": "315"}, {"size": 10083, "mtime": 1751196935984, "results": "397", "hashOfConfig": "315"}, {"size": 8134, "mtime": 1751196935989, "results": "398", "hashOfConfig": "315"}, {"size": 17474, "mtime": 1751196935997, "results": "399", "hashOfConfig": "315"}, {"size": 11033, "mtime": 1751196935997, "results": "400", "hashOfConfig": "315"}, {"size": 20468, "mtime": 1751196936008, "results": "401", "hashOfConfig": "315"}, {"size": 582, "mtime": 1750575923371, "results": "402", "hashOfConfig": "315"}, {"size": 859, "mtime": 1751196936010, "results": "403", "hashOfConfig": "315"}, {"size": 1491, "mtime": 1750612012115, "results": "404", "hashOfConfig": "315"}, {"size": 1443, "mtime": 1751198327172, "results": "405", "hashOfConfig": "315"}, {"size": 5537, "mtime": 1751196936016, "results": "406", "hashOfConfig": "315"}, {"size": 8573, "mtime": 1751196936022, "results": "407", "hashOfConfig": "315"}, {"size": 622, "mtime": 1750575923398, "results": "408", "hashOfConfig": "315"}, {"size": 7987, "mtime": 1751305336592, "results": "409", "hashOfConfig": "315"}, {"size": 4414, "mtime": 1751196936022, "results": "410", "hashOfConfig": "315"}, {"size": 8171, "mtime": 1751196936028, "results": "411", "hashOfConfig": "315"}, {"size": 11042, "mtime": 1751196936034, "results": "412", "hashOfConfig": "315"}, {"size": 2106, "mtime": 1751197247763, "results": "413", "hashOfConfig": "315"}, {"size": 9526, "mtime": 1751378320039, "results": "414", "hashOfConfig": "315"}, {"size": 6366, "mtime": 1751196936041, "results": "415", "hashOfConfig": "315"}, {"size": 9152, "mtime": 1751378320048, "results": "416", "hashOfConfig": "315"}, {"size": 4242, "mtime": 1750575923398, "results": "417", "hashOfConfig": "315"}, {"size": 2782, "mtime": 1751196936049, "results": "418", "hashOfConfig": "315"}, {"size": 10226, "mtime": 1751196936049, "results": "419", "hashOfConfig": "315"}, {"size": 1511, "mtime": 1750615669823, "results": "420", "hashOfConfig": "315"}, {"size": 9527, "mtime": 1751310828611, "results": "421", "hashOfConfig": "315"}, {"size": 4867, "mtime": 1751262377113, "results": "422", "hashOfConfig": "315"}, {"size": 11655, "mtime": 1751196936063, "results": "423", "hashOfConfig": "315"}, {"size": 11502, "mtime": 1751196936068, "results": "424", "hashOfConfig": "315"}, {"size": 12811, "mtime": 1751452002924, "results": "425", "hashOfConfig": "315"}, {"size": 8857, "mtime": 1751196936076, "results": "426", "hashOfConfig": "315"}, {"size": 7340, "mtime": 1751291766622, "results": "427", "hashOfConfig": "315"}, {"size": 5037, "mtime": 1751452002921, "results": "428", "hashOfConfig": "315"}, {"size": 2500, "mtime": 1751196936076, "results": "429", "hashOfConfig": "315"}, {"size": 9974, "mtime": 1751291766622, "results": "430", "hashOfConfig": "315"}, {"size": 16768, "mtime": 1751291766622, "results": "431", "hashOfConfig": "315"}, {"size": 6697, "mtime": 1751196936090, "results": "432", "hashOfConfig": "315"}, {"size": 12415, "mtime": 1751196936131, "results": "433", "hashOfConfig": "315"}, {"size": 12074, "mtime": 1751196936135, "results": "434", "hashOfConfig": "315"}, {"size": 14058, "mtime": 1751196936135, "results": "435", "hashOfConfig": "315"}, {"size": 2147, "mtime": 1751196936135, "results": "436", "hashOfConfig": "315"}, {"size": 4258, "mtime": 1750575923425, "results": "437", "hashOfConfig": "315"}, {"size": 3025, "mtime": 1751196936135, "results": "438", "hashOfConfig": "315"}, {"size": 4333, "mtime": 1751196936135, "results": "439", "hashOfConfig": "315"}, {"size": 2417, "mtime": 1751196936155, "results": "440", "hashOfConfig": "315"}, {"size": 717, "mtime": 1751196936155, "results": "441", "hashOfConfig": "315"}, {"size": 2325, "mtime": 1750575923425, "results": "442", "hashOfConfig": "315"}, {"size": 4452, "mtime": 1751196936169, "results": "443", "hashOfConfig": "315"}, {"size": 135, "mtime": 1751196936184, "results": "444", "hashOfConfig": "315"}, {"size": 2135, "mtime": 1750575923425, "results": "445", "hashOfConfig": "315"}, {"size": 7752, "mtime": 1751262412357, "results": "446", "hashOfConfig": "315"}, {"size": 1370, "mtime": 1751196936244, "results": "447", "hashOfConfig": "315"}, {"size": 6095, "mtime": 1750575923436, "results": "448", "hashOfConfig": "315"}, {"size": 11873, "mtime": 1751196936244, "results": "449", "hashOfConfig": "315"}, {"size": 2505, "mtime": 1750575923436, "results": "450", "hashOfConfig": "315"}, {"size": 6483, "mtime": 1750575923436, "results": "451", "hashOfConfig": "315"}, {"size": 4310, "mtime": 1750575923436, "results": "452", "hashOfConfig": "315"}, {"size": 6374, "mtime": 1750575923436, "results": "453", "hashOfConfig": "315"}, {"size": 803, "mtime": 1751196936309, "results": "454", "hashOfConfig": "315"}, {"size": 486, "mtime": 1751196936324, "results": "455", "hashOfConfig": "315"}, {"size": 8405, "mtime": 1751196936337, "results": "456", "hashOfConfig": "315"}, {"size": 1109, "mtime": 1750615180956, "results": "457", "hashOfConfig": "315"}, {"size": 1723, "mtime": 1751196936337, "results": "458", "hashOfConfig": "315"}, {"size": 7728, "mtime": 1751296181393, "results": "459", "hashOfConfig": "315"}, {"size": 2658, "mtime": 1751196936377, "results": "460", "hashOfConfig": "315"}, {"size": 139, "mtime": 1751197068957, "results": "461", "hashOfConfig": "315"}, {"size": 2949, "mtime": 1751196936377, "results": "462", "hashOfConfig": "315"}, {"size": 6869, "mtime": 1751196936377, "results": "463", "hashOfConfig": "315"}, {"size": 2385, "mtime": 1751196936377, "results": "464", "hashOfConfig": "315"}, {"size": 6605, "mtime": 1750575923441, "results": "465", "hashOfConfig": "315"}, {"size": 6960, "mtime": 1751196936390, "results": "466", "hashOfConfig": "315"}, {"size": 184, "mtime": 1751198341647, "results": "467", "hashOfConfig": "315"}, {"size": 8704, "mtime": 1751198271757, "results": "468", "hashOfConfig": "315"}, {"size": 147, "mtime": 1751197061209, "results": "469", "hashOfConfig": "315"}, {"size": 1119, "mtime": 1751177046067, "results": "470", "hashOfConfig": "315"}, {"size": 630, "mtime": 1750575923441, "results": "471", "hashOfConfig": "315"}, {"size": 1463, "mtime": 1750575923441, "results": "472", "hashOfConfig": "315"}, {"size": 214, "mtime": 1750575923459, "results": "473", "hashOfConfig": "315"}, {"size": 5440, "mtime": 1751307798433, "results": "474", "hashOfConfig": "315"}, {"size": 174, "mtime": 1751197077152, "results": "475", "hashOfConfig": "315"}, {"size": 4297, "mtime": 1751196936406, "results": "476", "hashOfConfig": "315"}, {"size": 4803, "mtime": 1751434095715, "results": "477", "hashOfConfig": "315"}, {"size": 97, "mtime": 1751197085063, "results": "478", "hashOfConfig": "315"}, {"size": 112, "mtime": 1751197092770, "results": "479", "hashOfConfig": "315"}, {"size": 2218, "mtime": 1750575923441, "results": "480", "hashOfConfig": "315"}, {"size": 5357, "mtime": 1751228764956, "results": "481", "hashOfConfig": "315"}, {"size": 6251, "mtime": 1751196936420, "results": "482", "hashOfConfig": "315"}, {"size": 4561, "mtime": 1750575923441, "results": "483", "hashOfConfig": "315"}, {"size": 3034, "mtime": 1751196936429, "results": "484", "hashOfConfig": "315"}, {"size": 3557, "mtime": 1751301373374, "results": "485", "hashOfConfig": "315"}, {"size": 17279, "mtime": 1751196936433, "results": "486", "hashOfConfig": "315"}, {"size": 2136, "mtime": 1751196936460, "results": "487", "hashOfConfig": "315"}, {"size": 566, "mtime": 1750575923441, "results": "488", "hashOfConfig": "315"}, {"size": 165, "mtime": 1750575923455, "results": "489", "hashOfConfig": "315"}, {"size": 5248, "mtime": 1751196936487, "results": "490", "hashOfConfig": "315"}, {"size": 8082, "mtime": 1751196936495, "results": "491", "hashOfConfig": "315"}, {"size": 2617, "mtime": 1751291766622, "results": "492", "hashOfConfig": "315"}, {"size": 929, "mtime": 1751301958057, "results": "493", "hashOfConfig": "315"}, {"size": 3021, "mtime": 1750575923461, "results": "494", "hashOfConfig": "315"}, {"size": 12242, "mtime": 1751440653354, "results": "495", "hashOfConfig": "315"}, {"size": 7168, "mtime": 1751202537764, "results": "496", "hashOfConfig": "315"}, {"size": 8309, "mtime": 1751378320055, "results": "497", "hashOfConfig": "315"}, {"size": 3053, "mtime": 1750575923462, "results": "498", "hashOfConfig": "315"}, {"size": 9925, "mtime": 1751469125706, "results": "499", "hashOfConfig": "315"}, {"size": 1882, "mtime": 1750575923462, "results": "500", "hashOfConfig": "315"}, {"size": 4833, "mtime": 1751196936564, "results": "501", "hashOfConfig": "315"}, {"size": 2162, "mtime": 1751196936564, "results": "502", "hashOfConfig": "315"}, {"size": 4524, "mtime": 1751196936580, "results": "503", "hashOfConfig": "315"}, {"size": 7836, "mtime": 1751196936582, "results": "504", "hashOfConfig": "315"}, {"size": 4167, "mtime": 1751203252731, "results": "505", "hashOfConfig": "315"}, {"size": 224, "mtime": 1751196936596, "results": "506", "hashOfConfig": "315"}, {"size": 501, "mtime": 1750575923462, "results": "507", "hashOfConfig": "315"}, {"size": 4891, "mtime": 1750575923462, "results": "508", "hashOfConfig": "315"}, {"size": 1791, "mtime": 1751259810669, "results": "509", "hashOfConfig": "315"}, {"size": 3652, "mtime": 1751432027356, "results": "510", "hashOfConfig": "315"}, {"size": 2447, "mtime": 1750600597419, "results": "511", "hashOfConfig": "315"}, {"size": 3083, "mtime": 1750575923472, "results": "512", "hashOfConfig": "315"}, {"size": 3949, "mtime": 1751378320055, "results": "513", "hashOfConfig": "315"}, {"size": 2356, "mtime": 1751196936607, "results": "514", "hashOfConfig": "315"}, {"size": 565, "mtime": 1751196936607, "results": "515", "hashOfConfig": "315"}, {"size": 390, "mtime": 1750577887298, "results": "516", "hashOfConfig": "315"}, {"size": 6615, "mtime": 1751468236607, "results": "517", "hashOfConfig": "315"}, {"size": 373, "mtime": 1751196958818, "results": "518", "hashOfConfig": "315"}, {"size": 299, "mtime": 1751196151030, "results": "519", "hashOfConfig": "315"}, {"size": 332, "mtime": 1751196948703, "results": "520", "hashOfConfig": "315"}, {"size": 286, "mtime": 1751196967098, "results": "521", "hashOfConfig": "315"}, {"size": 44187, "mtime": 1750575923568, "results": "522", "hashOfConfig": "315"}, {"size": 398, "mtime": 1751196979580, "results": "523", "hashOfConfig": "315"}, {"size": 4732, "mtime": 1750853448916, "results": "524", "hashOfConfig": "315"}, {"size": 5868, "mtime": 1750575923519, "results": "525", "hashOfConfig": "315"}, {"size": 4141, "mtime": 1751197392262, "results": "526", "hashOfConfig": "315"}, {"size": 8072, "mtime": 1751198271769, "results": "527", "hashOfConfig": "315"}, {"size": 3715, "mtime": 1750575923519, "results": "528", "hashOfConfig": "315"}, {"size": 5943, "mtime": 1750575923519, "results": "529", "hashOfConfig": "315"}, {"size": 9375, "mtime": 1750575923519, "results": "530", "hashOfConfig": "315"}, {"size": 134, "mtime": 1751197112565, "results": "531", "hashOfConfig": "315"}, {"size": 10403, "mtime": 1750575923519, "results": "532", "hashOfConfig": "315"}, {"size": 4149, "mtime": 1750575923519, "results": "533", "hashOfConfig": "315"}, {"size": 7483, "mtime": 1750575923519, "results": "534", "hashOfConfig": "315"}, {"size": 5633, "mtime": 1750575923519, "results": "535", "hashOfConfig": "315"}, {"size": 2746, "mtime": 1750575923519, "results": "536", "hashOfConfig": "315"}, {"size": 699, "mtime": 1751197435678, "results": "537", "hashOfConfig": "315"}, {"size": 6262, "mtime": 1751196936631, "results": "538", "hashOfConfig": "315"}, {"size": 1800, "mtime": 1750575923519, "results": "539", "hashOfConfig": "315"}, {"size": 3162, "mtime": 1751177046077, "results": "540", "hashOfConfig": "315"}, {"size": 4293, "mtime": 1751197405221, "results": "541", "hashOfConfig": "315"}, {"size": 1397, "mtime": 1750575923519, "results": "542", "hashOfConfig": "315"}, {"size": 2896, "mtime": 1750575923519, "results": "543", "hashOfConfig": "315"}, {"size": 14874, "mtime": 1751196935777, "results": "544", "hashOfConfig": "315"}, {"size": 115, "mtime": 1751196640840, "results": "545", "hashOfConfig": "315"}, {"size": 3467, "mtime": 1751196935782, "results": "546", "hashOfConfig": "315"}, {"size": 91, "mtime": 1751196674300, "results": "547", "hashOfConfig": "315"}, {"size": 1311, "mtime": 1751262212428, "results": "548", "hashOfConfig": "315"}, {"size": 3486, "mtime": 1750575923493, "results": "549", "hashOfConfig": "315"}, {"size": 4171, "mtime": 1750795292502, "results": "550", "hashOfConfig": "315"}, {"size": 196, "mtime": 1751202550804, "results": "551", "hashOfConfig": "315"}, {"size": 310, "mtime": 1751258874931, "results": "552", "hashOfConfig": "315"}, {"size": 7299, "mtime": 1751196935782, "results": "553", "hashOfConfig": "315"}, {"size": 18998, "mtime": 1751203029659, "results": "554", "hashOfConfig": "315"}, {"size": 7069, "mtime": 1751177046073, "results": "555", "hashOfConfig": "315"}, {"size": 10228, "mtime": 1750787828516, "results": "556", "hashOfConfig": "315"}, {"size": 12605, "mtime": 1751177046074, "results": "557", "hashOfConfig": "315"}, {"size": 2486, "mtime": 1751204700341, "results": "558", "hashOfConfig": "315"}, {"size": 8996, "mtime": 1750865742183, "results": "559", "hashOfConfig": "315"}, {"size": 244, "mtime": 1751202613508, "results": "560", "hashOfConfig": "315"}, {"size": 813, "mtime": 1751203054964, "results": "561", "hashOfConfig": "315"}, {"size": 5780, "mtime": 1750575923501, "results": "562", "hashOfConfig": "315"}, {"size": 13141, "mtime": 1750575923503, "results": "563", "hashOfConfig": "315"}, {"size": 9121, "mtime": 1751196935796, "results": "564", "hashOfConfig": "315"}, {"size": 8958, "mtime": 1750866379202, "results": "565", "hashOfConfig": "315"}, {"size": 10184, "mtime": 1750575923503, "results": "566", "hashOfConfig": "315"}, {"size": 14026, "mtime": 1751202770312, "results": "567", "hashOfConfig": "315"}, {"size": 9347, "mtime": 1750575923509, "results": "568", "hashOfConfig": "315"}, {"size": 10267, "mtime": 1751196935809, "results": "569", "hashOfConfig": "315"}, {"size": 9824, "mtime": 1750870230820, "results": "570", "hashOfConfig": "315"}, {"size": 9929, "mtime": 1751291766606, "results": "571", "hashOfConfig": "315"}, {"size": 1514, "mtime": 1751204815410, "results": "572", "hashOfConfig": "315"}, {"size": 3873, "mtime": 1751262975209, "results": "573", "hashOfConfig": "315"}, {"size": 408, "mtime": 1751203069163, "results": "574", "hashOfConfig": "315"}, {"size": 857, "mtime": 1751198050893, "results": "575", "hashOfConfig": "315"}, {"size": 112, "mtime": 1751196649882, "results": "576", "hashOfConfig": "315"}, {"size": 5305, "mtime": 1751262177734, "results": "577", "hashOfConfig": "315"}, {"size": 6017, "mtime": 1750741154161, "results": "578", "hashOfConfig": "315"}, {"size": 4903, "mtime": 1750741251063, "results": "579", "hashOfConfig": "315"}, {"size": 199, "mtime": 1751196683686, "results": "580", "hashOfConfig": "315"}, {"size": 7591, "mtime": 1750741228357, "results": "581", "hashOfConfig": "315"}, {"size": 141, "mtime": 1751196622657, "results": "582", "hashOfConfig": "315"}, {"size": 6390, "mtime": 1750575923503, "results": "583", "hashOfConfig": "315"}, {"size": 241, "mtime": 1751204860041, "results": "584", "hashOfConfig": "315"}, {"size": 12362, "mtime": 1751203084783, "results": "585", "hashOfConfig": "315"}, {"size": 872, "mtime": 1751204892055, "results": "586", "hashOfConfig": "315"}, {"size": 110, "mtime": 1751196631522, "results": "587", "hashOfConfig": "315"}, {"size": 2484, "mtime": 1751228547509, "results": "588", "hashOfConfig": "315"}, {"size": 122, "mtime": 1751196661692, "results": "589", "hashOfConfig": "315"}, {"size": 16096, "mtime": 1750575923509, "results": "590", "hashOfConfig": "315"}, {"size": 11977, "mtime": 1751291766606, "results": "591", "hashOfConfig": "315"}, {"size": 4914, "mtime": 1751197366255, "results": "592", "hashOfConfig": "315"}, {"size": 4883, "mtime": 1751197379381, "results": "593", "hashOfConfig": "315"}, {"size": 9401, "mtime": 1751196935835, "results": "594", "hashOfConfig": "315"}, {"size": 608, "mtime": 1751202926556, "results": "595", "hashOfConfig": "315"}, {"size": 10131, "mtime": 1751291766606, "results": "596", "hashOfConfig": "315"}, {"size": 571, "mtime": 1751197793865, "results": "597", "hashOfConfig": "315"}, {"size": 5386, "mtime": 1750575923519, "results": "598", "hashOfConfig": "315"}, {"size": 4723, "mtime": 1750865308830, "results": "599", "hashOfConfig": "315"}, {"size": 1088, "mtime": 1751202969783, "results": "600", "hashOfConfig": "315"}, {"size": 1017, "mtime": 1751177046076, "results": "601", "hashOfConfig": "315"}, {"size": 5163, "mtime": 1750575923519, "results": "602", "hashOfConfig": "315"}, {"size": 745, "mtime": 1751197805491, "results": "603", "hashOfConfig": "315"}, {"size": 6793, "mtime": 1750575923519, "results": "604", "hashOfConfig": "315"}, {"size": 765, "mtime": 1751197817016, "results": "605", "hashOfConfig": "315"}, {"size": 8425, "mtime": 1750575923519, "results": "606", "hashOfConfig": "315"}, {"size": 320, "mtime": 1751197763583, "results": "607", "hashOfConfig": "315"}, {"size": 386, "mtime": 1751197716471, "results": "608", "hashOfConfig": "315"}, {"size": 522, "mtime": 1751291766606, "results": "609", "hashOfConfig": "315"}, {"size": 402, "mtime": 1751197782239, "results": "610", "hashOfConfig": "315"}, {"size": 338, "mtime": 1751197734938, "results": "611", "hashOfConfig": "315"}, {"size": 481, "mtime": 1751196097814, "results": "612", "hashOfConfig": "315"}, {"size": 396, "mtime": 1751197746117, "results": "613", "hashOfConfig": "315"}, {"size": 375, "mtime": 1751197754372, "results": "614", "hashOfConfig": "315"}, {"size": 382, "mtime": 1751197772875, "results": "615", "hashOfConfig": "315"}, {"size": 2704, "mtime": 1751380574388, "results": "616", "hashOfConfig": "315"}, {"size": 8236, "mtime": 1751306237260, "results": "617", "hashOfConfig": "315"}, {"size": 5286, "mtime": 1751306252227, "results": "618", "hashOfConfig": "315"}, {"size": 6168, "mtime": 1751310451393, "results": "619", "hashOfConfig": "315"}, {"size": 7726, "mtime": 1751310592239, "results": "620", "hashOfConfig": "315"}, {"size": 2683, "mtime": 1751380496260, "results": "621", "hashOfConfig": "315"}, {"size": 3755, "mtime": 1751309969653, "results": "622", "hashOfConfig": "315"}, {"size": 7437, "mtime": 1751436144849, "results": "623", "hashOfConfig": "315"}, {"size": 8193, "mtime": 1751470750749, "results": "624", "hashOfConfig": "315"}, {"size": 3075, "mtime": 1751462923580, "results": "625", "hashOfConfig": "315"}, {"size": 6742, "mtime": 1751469330188, "results": "626", "hashOfConfig": "315"}, {"size": 701, "mtime": 1751470301143, "results": "627", "hashOfConfig": "315"}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "64mijf", {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1372", "messages": "1373", "suppressedMessages": "1374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1423", "messages": "1424", "suppressedMessages": "1425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1426", "messages": "1427", "suppressedMessages": "1428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1429", "messages": "1430", "suppressedMessages": "1431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1432", "messages": "1433", "suppressedMessages": "1434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1435", "messages": "1436", "suppressedMessages": "1437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1438", "messages": "1439", "suppressedMessages": "1440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1441", "messages": "1442", "suppressedMessages": "1443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1444", "messages": "1445", "suppressedMessages": "1446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1447", "messages": "1448", "suppressedMessages": "1449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1450", "messages": "1451", "suppressedMessages": "1452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1453", "messages": "1454", "suppressedMessages": "1455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1456", "messages": "1457", "suppressedMessages": "1458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1459", "messages": "1460", "suppressedMessages": "1461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1462", "messages": "1463", "suppressedMessages": "1464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1465", "messages": "1466", "suppressedMessages": "1467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1468", "messages": "1469", "suppressedMessages": "1470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1471", "messages": "1472", "suppressedMessages": "1473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1474", "messages": "1475", "suppressedMessages": "1476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1477", "messages": "1478", "suppressedMessages": "1479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1480", "messages": "1481", "suppressedMessages": "1482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1483", "messages": "1484", "suppressedMessages": "1485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1486", "messages": "1487", "suppressedMessages": "1488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1489", "messages": "1490", "suppressedMessages": "1491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1492", "messages": "1493", "suppressedMessages": "1494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1495", "messages": "1496", "suppressedMessages": "1497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1498", "messages": "1499", "suppressedMessages": "1500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1501", "messages": "1502", "suppressedMessages": "1503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1504", "messages": "1505", "suppressedMessages": "1506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1507", "messages": "1508", "suppressedMessages": "1509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1510", "messages": "1511", "suppressedMessages": "1512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1513", "messages": "1514", "suppressedMessages": "1515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1516", "messages": "1517", "suppressedMessages": "1518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1519", "messages": "1520", "suppressedMessages": "1521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1522", "messages": "1523", "suppressedMessages": "1524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1525", "messages": "1526", "suppressedMessages": "1527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1528", "messages": "1529", "suppressedMessages": "1530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1531", "messages": "1532", "suppressedMessages": "1533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1534", "messages": "1535", "suppressedMessages": "1536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1537", "messages": "1538", "suppressedMessages": "1539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1540", "messages": "1541", "suppressedMessages": "1542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1543", "messages": "1544", "suppressedMessages": "1545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1546", "messages": "1547", "suppressedMessages": "1548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1549", "messages": "1550", "suppressedMessages": "1551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1552", "messages": "1553", "suppressedMessages": "1554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1555", "messages": "1556", "suppressedMessages": "1557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1558", "messages": "1559", "suppressedMessages": "1560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1561", "messages": "1562", "suppressedMessages": "1563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1564", "messages": "1565", "suppressedMessages": "1566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\likes.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\reviews.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\activity\\subscriptions.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\edit-profile.tsx", [], ["1567"], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\reviews.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\subscriptions.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\+not-found.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx", [], ["1568", "1569"], "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\UserDataContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ad.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\components.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\screens.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ui.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\activityService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\activities\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\adService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\ads\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\authService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\emailOtpService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\googleAuthService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\mobileAuthService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\auth\\nativeGoogleAuth2025.ts", [], ["1570"], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessCardDataService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessDiscovery.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessOnboardingService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessPostService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\businessProfileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\metricsService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\offlineService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\onboardingService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileCheckService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\profileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\qrScanService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\settingsService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\syncService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\common\\userRoleStatusService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\batchProfileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerPostService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\customerProfileService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\location\\locationService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\developmentErrorMonitoring.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\errorTracking.ts", [], ["1571"], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\monitoring\\productionErrorLogging.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\postService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\socialService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\posts\\unifiedFeedService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\products\\productService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\realtime\\realtimeService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\avatarUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\businessPostImageUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\customerPostImageUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\imageUploadService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\services\\storage\\storageService.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\businessSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\locationUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\profileValidation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\supabase\\utils\\validation.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\activities.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\business.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\common.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\customer.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\posts.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\products.ts", [], [], "C:\\web-app\\dukancard-app\\backend\\types\\storage.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\NetworkStatusBanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordUpdateSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\useClientOnlyValue.ts", [], [], {"ruleId": "1572", "severity": 1, "message": "1573", "line": 30, "column": 6, "nodeType": "1574", "endLine": 30, "endColumn": 8, "suggestions": "1575", "suppressions": "1576"}, {"ruleId": "1572", "severity": 1, "message": "1577", "line": 469, "column": 6, "nodeType": "1574", "endLine": 469, "endColumn": 56, "suggestions": "1578", "suppressions": "1579"}, {"ruleId": "1572", "severity": 1, "message": "1580", "line": 498, "column": 6, "nodeType": "1574", "endLine": 498, "endColumn": 100, "suggestions": "1581", "suppressions": "1582"}, {"ruleId": "1583", "severity": 1, "message": "1584", "line": 12, "column": 30, "nodeType": "1585", "messageId": "1586", "endLine": 12, "endColumn": 82, "suppressions": "1587"}, {"ruleId": "1583", "severity": 1, "message": "1584", "line": 54, "column": 33, "nodeType": "1585", "messageId": "1586", "endLine": 54, "endColumn": 89, "suppressions": "1588"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCustomerProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["1589"], ["1590"], "React Hook useCallback has missing dependencies: 'styles.loadingFooter' and 'styles.loadingText'. Either include them or remove the dependency array.", ["1591"], ["1592"], "React Hook useCallback has missing dependencies: 'styles.emptyContainer', 'styles.emptySubtitle', 'styles.emptyTitle', and 'styles.skeletonContainer'. Either include them or remove the dependency array.", ["1593"], ["1594"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["1595"], ["1596"], {"desc": "1597", "fix": "1598"}, {"kind": "1599", "justification": "1600"}, {"desc": "1601", "fix": "1602"}, {"kind": "1599", "justification": "1600"}, {"desc": "1603", "fix": "1604"}, {"kind": "1599", "justification": "1600"}, {"kind": "1599", "justification": "1600"}, {"kind": "1599", "justification": "1600"}, "Update the dependencies array to be: [fetchCustomerProfile]", {"range": "1605", "text": "1606"}, "directive", "", "Update the dependencies array to be: [isLoading, hasMore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", {"range": "1607", "text": "1608"}, "Update the dependencies array to be: [isLoadingFromCache, posts.length, isLoading, styles.skeletonContainer, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", {"range": "1609", "text": "1610"}, [1050, 1052], "[fetchCustomerProfile]", [16022, 16072], "[isLoading, has<PERSON>ore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", [17059, 17153], "[isLoading<PERSON><PERSON><PERSON><PERSON>, posts.length, isLoading, styles.skeletonContainer, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]"]