
import { supabase } from '@/lib/supabase';
import React, { useState, useTransition } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { Mail, Link, AlertCircle } from 'lucide-react-native';

interface LinkEmailSectionProps {
  currentEmail?: string | null | undefined;
  currentPhone?: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function LinkEmailSection({
  currentEmail,
  registrationType
}: LinkEmailSectionProps) {
  const [isPending, startTransition] = useTransition();
  const [message, setMessage] = useState<string | null>(null);
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [emailForOTP, setEmailForOTP] = useState<string>('');
  const [email, setEmail] = useState(registrationType === 'email' ? (currentEmail || "") : "");
  const [otp, setOtp] = useState("");

  const onEmailSubmit = () => {
    startTransition(async () => {
      if (email) {
        const { error } = await supabase.auth.updateUser({ email });
        if (error) {
          Alert.alert("Error", error.message);
        } else {
          if (registrationType === 'phone') {
            setEmailForOTP(email);
            setStep('otp');
            setMessage(`We&#39;ve sent a 6-digit verification code to your email address.`);
          } else {
            setMessage("Please check your email for the verification link.");
          }
        }
      } else {
        Alert.alert("Error", "Please enter a valid email address");
      }
    });
  };

  const onOTPSubmit = () => {
    startTransition(async () => {
      if (otp.length === 6) {
        const { error } = await supabase.auth.verifyOtp({ email: emailForOTP, token: otp, type: 'email' });
        if (error) {
          Alert.alert("Error", error.message);
        } else {
          setMessage("Email address has been linked to your account.");
          setStep('email');
          setEmailForOTP('');
          setEmail('');
          setOtp('');
        }
      } else {
        Alert.alert("Error", "OTP must be 6 digits");
      }
    });
  };

  const onBackToEmail = () => {
    setStep('email');
    setEmailForOTP('');
    setMessage(null);
    setOtp('');
  };

  const isGoogleUser = registrationType === 'google';
  const isEmailUser = registrationType === 'email';

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <View style={styles.headerIconContainer}>
          <Mail size={20} color="#2563EB" />
        </View>
        <Text style={styles.cardTitle}>
          {isGoogleUser ? "Email Address (Google)" : isEmailUser ? "Update Email Address" : "Link Email Address"}
        </Text>
      </View>
      <View style={styles.cardContent}>
        {message && (
          <View style={styles.messageContainer}>
            <AlertCircle size={20} color="#2563EB" />
            <Text style={styles.messageText}>{message}</Text>
          </View>
        )}

        {isGoogleUser ? (
          <View>
            <Text style={styles.label}>Email Address</Text>
            <View style={styles.readOnlyContainer}>
              <Text style={styles.readOnlyText}>{currentEmail}</Text>
            </View>
            <Text style={styles.description}>This email is managed by your Google account.</Text>
          </View>
        ) : step === 'email' ? (
          <View>
            <Text style={styles.label}>Email Address</Text>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              editable={!isPending}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <Text style={styles.description}>
              {isEmailUser
                ? "We'll send verification codes to both your current and new email addresses."
                : "We'll send a verification code to this email address."
              }
            </Text>
            <TouchableOpacity
              style={[styles.button, (isPending || !email) && styles.buttonDisabled]}
              onPress={onEmailSubmit}
              disabled={isPending || !email}
            >
              {isPending ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <>
                  <Link size={20} color="#FFFFFF" style={{ marginRight: 8 }} />
                  <Text style={styles.buttonText}>{isEmailUser ? "Update Email" : "Link Email Address"}</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        ) : (
          <View>
            <Text style={styles.otpPrompt}>
              We&apos;ve sent a 6-digit code to <Text style={{ fontWeight: 'bold' }}>{emailForOTP}</Text>
            </Text>
            <Text style={styles.label}>Enter Verification Code</Text>
            <TextInput
              style={styles.input}
              value={otp}
              onChangeText={setOtp}
              editable={!isPending}
              keyboardType="number-pad"
              maxLength={6}
            />
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.backButton]}
                onPress={onBackToEmail}
                disabled={isPending}
              >
                <Text style={styles.backButtonText}>Back to Email</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, (isPending || otp.length !== 6) && styles.buttonDisabled]}
                onPress={onOTPSubmit}
                disabled={isPending || otp.length !== 6}
              >
                {isPending ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <>
                    <Link size={20} color="#FFFFFF" style={{ marginRight: 8 }} />
                    <Text style={styles.buttonText}>Verify & Link Email</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  headerIconContainer: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#DBEAFE',
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A202C',
  },
  cardContent: {
    padding: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#EFF6FF',
    marginBottom: 16,
  },
  messageText: {
    marginLeft: 8,
    color: '#1E40AF',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4A5568',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#CBD5E0',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#F7FAFC',
  },
  readOnlyContainer: {
    padding: 12,
    backgroundColor: '#F7FAFC',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  readOnlyText: {
    color: '#718096',
  },
  description: {
    fontSize: 12,
    color: '#718096',
    marginTop: 8,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2563EB',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  buttonDisabled: {
    backgroundColor: '#93C5FD',
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  otpPrompt: {
    textAlign: 'center',
    marginBottom: 16,
    color: '#4A5568',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  backButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#CBD5E0',
  },
  backButtonText: {
    color: '#4A5568',
  },
});
