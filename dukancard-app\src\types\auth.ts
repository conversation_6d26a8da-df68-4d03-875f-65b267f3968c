/**
 * Enhanced TypeScript types for authentication and user management
 * Based on dukancard/app/(onboarding)/onboarding/types/onboarding.ts
 */


// User role types
export type UserRole = 'customer' | 'business' | null;

// Profile status types
export type ProfileStatus = 'none' | 'customer' | 'business_incomplete' | 'business_complete';

// Onboarding status for business users
export type OnboardingStatus = 'not_started' | 'in_progress' | 'completed';

// Authentication state
export type AuthState = 'loading' | 'authenticated' | 'unauthenticated';

// Enhanced user type based on Supabase User
export interface AuthUser {
  id: string;
  email?: string;
  phone?: string;
  user_metadata?: {
    full_name?: string;
    name?: string;
    avatar_url?: string;
    display_name?: string;
    [key: string]: unknown;
  };
  app_metadata?: {
    provider?: string;
    [key: string]: unknown;
  };
  created_at?: string;
  updated_at?: string;
  email_confirmed_at?: string;
  phone_confirmed_at?: string;
}

// User role status interface
export interface UserRoleStatus {
  hasCustomerProfile: boolean;
  hasBusinessProfile: boolean;
  businessOnboardingCompleted: boolean;
  role: UserRole;
  needsRoleSelection: boolean;
  needsOnboarding: boolean;
}

// Profile status interface
export interface UserProfileStatus {
  loading: boolean;
  roleStatus: UserRoleStatus | null;
  error: any;
}

// Customer profile type
export interface CustomerProfile {
  id: string;
  name?: string | null;
  email?: string | null;
  phone?: string | null;
  phone_number?: string | null;
  locality_slug?: string | null;
  city_slug?: string | null;
  state_slug?: string | null;
  pincode?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Business profile type (basic structure)
export interface BusinessProfile {
  id: string;
  business_name?: string | null;
  business_slug?: string | null;
  logo_url?: string | null;
  business_category?: string | null;
  member_name?: string | null;
  title?: string | null;
  contact_email?: string | null;
  phone?: string | null;
  whatsapp?: string | null;
  bio?: string | null;
  address_line?: string | null;
  pincode?: string | null;
  city?: string | null;
  state?: string | null;
  locality?: string | null;
  business_status?: 'online' | 'offline';
  total_likes?: number | null;
  total_subscriptions?: number | null;
  average_rating?: number | null;
  // Note: onboarding completion is determined by presence of business_slug
  trial_end_date?: string | null;
  has_active_subscription?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Onboarding form data structure
export interface OnboardingFormData {
  // Step 1: Business Details
  businessName: string;
  businessCategory: string;
  memberName: string;
  establishedYear?: string;
  
  // Step 2: Card Information
  businessSlug: string;
  title: string;
  bio?: string;
  phone: string;
  email: string;
  whatsapp?: string;
  
  // Step 3: Address Information
  pincode: string;
  locality: string;
  addressLine: string;
  city: string;
  state: string;
  businessStatus: 'online' | 'offline';
  
  // Step 4: Plan Selection
  planId: string;
}

// Existing business profile data for pre-filling forms
export interface ExistingBusinessProfileData {
  businessName?: string;
  businessCategory?: string;
  memberName?: string;
  establishedYear?: string;
  businessSlug?: string;
  title?: string;
  bio?: string;
  phone?: string;
  email?: string;
  whatsapp?: string;
  addressLine?: string;
  pincode?: string;
  locality?: string;
  city?: string;
  state?: string;
  businessStatus?: 'online' | 'offline';
  planId?: string;
  hasExistingSubscription?: boolean;
}

// Onboarding state management
export interface OnboardingState {
  currentStep: number;
  totalSteps: number;
  formData: Partial<OnboardingFormData>;
  isSubmitting: boolean;
  errors: Record<string, string>;
  slugAvailable: boolean | null;
  isCheckingSlug: boolean;
  selectedPlan: any | null; // Will be defined when implementing plan selection
  showPlans: boolean;
  existingData: ExistingBusinessProfileData | null;
  isLoadingExistingData: boolean;
}

// Navigation and routing types
export interface NavigationState {
  redirectPath?: string;
  redirectSlug?: string;
  message?: string;
  fromCard?: boolean;
}

// Authentication context type
export interface AuthContextType {
  user: AuthUser | null;
  session: any | null;
  loading: boolean;
  profileStatus: UserProfileStatus;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signOut: () => Promise<{ error: any }>;
  signInWithGoogle: () => Promise<{ error: any }>;
  checkUserRole: () => Promise<UserRoleStatus | null>;
  hasCompletedOnboarding: () => boolean;
  getUserProfile: () => Promise<CustomerProfile | BusinessProfile | null>;
  refreshProfileStatus: () => Promise<void>;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Location and address types
export interface LocationData {
  pincode: string;
  locality: string;
  city: string;
  state: string;
  country?: string;
}

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: 'granted' | 'denied' | 'undetermined';
}

// Role selection types
export interface RoleOption {
  id: UserRole;
  title: string;
  description: string;
  icon: string;
  features: string[];
  recommended?: boolean;
}

// Authentication error types
export interface AuthError {
  code?: string;
  message: string;
  details?: any;
}

// Step navigation types
export interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
  onNext: () => void;
  onPrevious: () => void;
  onStepClick?: (step: number) => void;
}

// Form field types
export interface FormFieldConfig {
  name: keyof OnboardingFormData;
  label: string;
  type: 'text' | 'email' | 'tel' | 'select' | 'textarea';
  placeholder?: string;
  required?: boolean;
  validation?: any;
  options?: { label: string; value: string }[];
}

// Onboarding step configuration
export interface OnboardingStep {
  id: number;
  title: string;
  description: string;
  fields: FormFieldConfig[];
  validation?: any;
  component?: string;
}
