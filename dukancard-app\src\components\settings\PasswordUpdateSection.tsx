
import { supabase } from '@/lib/supabase';
import React, { useTransition } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { KeyRound, Info } from 'lucide-react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const PasswordSchema = yup.object().shape({
  currentPassword: yup.string().required("Current password is required"),
  newPassword: yup.string().min(6, "Password must be at least 6 characters").required("New password is required"),
  confirmPassword: yup.string().oneOf([yup.ref('newPassword'), undefined], "Passwords must match").required("Please confirm your password"),
});

interface PasswordUpdateSectionProps {
  registrationType: 'google' | 'email' | 'phone';
}

export default function PasswordUpdateSection({
  registrationType,
}: PasswordUpdateSectionProps) {
  const isGoogleLogin = registrationType === 'google';
  const [isPending, startTransition] = useTransition();

  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(PasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

const onPasswordSubmit = (data: any) => {
  startTransition(async () => {
    const { error } = await supabase.auth.updateUser({ password: data.newPassword });
    if (error) {
      Alert.alert("Error", error.message);
    } else {
      Alert.alert("Success", "Password updated successfully!");
    }
  });
};


  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <View style={styles.headerIconContainer}>
          <KeyRound size={20} color="#D97706" />
        </View>
        <Text style={styles.cardTitle}>Password</Text>
      </View>
      <View style={styles.cardContent}>
        {isGoogleLogin ? (
          <View style={styles.infoContainer}>
            <Info size={20} color="#F59E0B" />
            <Text style={styles.infoText}>
              You signed up with Google. Password management is handled by your Google account.
            </Text>
          </View>
        ) : (
          <View>
            <Controller
              control={control}
              name="currentPassword"
              render={({ field: { onChange, onBlur, value } }) => (
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>Current Password</Text>
                  <TextInput
                    style={styles.input}
                    secureTextEntry
                    placeholder="••••••••"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    editable={!isPending}
                  />
                  {errors.currentPassword && <Text style={styles.errorText}>{errors.currentPassword.message}</Text>}
                </View>
              )}
            />
            <Controller
              control={control}
              name="newPassword"
              render={({ field: { onChange, onBlur, value } }) => (
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>New Password</Text>
                  <TextInput
                    style={styles.input}
                    secureTextEntry
                    placeholder="••••••••"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    editable={!isPending}
                  />
                  <Text style={styles.description}>Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol</Text>
                  {errors.newPassword && <Text style={styles.errorText}>{errors.newPassword.message}</Text>}
                </View>
              )}
            />
            <Controller
              control={control}
              name="confirmPassword"
              render={({ field: { onChange, onBlur, value } }) => (
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>Confirm Password</Text>
                  <TextInput
                    style={styles.input}
                    secureTextEntry
                    placeholder="••••••••"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    editable={!isPending}
                  />
                  {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword.message}</Text>}
                </View>
              )}
            />
            <TouchableOpacity
              style={[styles.button, isPending && styles.buttonDisabled]}
              onPress={handleSubmit(onPasswordSubmit)}
              disabled={isPending}
            >
              {isPending ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.buttonText}>Change Password</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  headerIconContainer: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FEF3C7',
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A202C',
  },
  cardContent: {
    padding: 16,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#FFFBEB',
  },
  infoText: {
    marginLeft: 8,
    color: '#B45309',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4A5568',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#CBD5E0',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#F7FAFC',
  },
  description: {
    fontSize: 12,
    color: '#718096',
    marginTop: 8,
  },
  errorText: {
    color: '#DC2626',
    marginTop: 4,
  },
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#D97706',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  buttonDisabled: {
    backgroundColor: '#FBBF24',
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
