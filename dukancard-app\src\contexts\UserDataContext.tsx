import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { supabase } from '@/lib/supabase';
import { getComprehensiveUserProfile } from '@/backend/supabase/services/customer/customerProfileService';
import { BusinessProfile } from '@/backend/types/business';
import { User } from '@supabase/supabase-js';
import { useAuth as useAuthContext } from './AuthContext';

interface CachedData<T> {
  data: T;
  lastFetched: number;
}

interface CustomerProfile {
  id: string;
  name: string | null;
  email: string | null;
  phone: string | null;
  avatar_url: string | null;
  address: string | null;
  pincode: string | null;
  city: string | null;
  state: string | null;
  locality: string | null;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  created_at: string;
  updated_at: string;
}



interface UserDataContextType {
  // User authentication
  user: User | null;
  isLoading: boolean;
  
  // Customer profile data
  customerProfile: CustomerProfile | null;
  
  // Business profile data  
  businessProfile: BusinessProfile | null;
  
  // Generic data cache
  dataCache: Map<string, CachedData<any>>;
  
  // Methods
  getUser: () => Promise<User | null>;
  getCustomerProfile: () => Promise<CustomerProfile | null>;
  getBusinessProfile: () => Promise<BusinessProfile | null>;
  getCachedData: (key: string) => any;
  setCachedData: (key: string, data: any) => void;
  clearCache: () => void;
  refreshUser: () => Promise<void>;
  refreshCustomerProfile: () => Promise<void>;
  refreshBusinessProfile: () => Promise<void>;
  refreshAll: () => Promise<void>;
}

const UserDataContext = createContext<UserDataContextType | undefined>(undefined);

interface UserDataProviderProps {
  children: ReactNode;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

export const UserDataProvider: React.FC<UserDataProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [customerProfile, setCustomerProfile] = useState<CustomerProfile | null>(null);
  const [businessProfile, setBusinessProfile] = useState<BusinessProfile | null>(null);
  const [dataCache, setDataCache] = useState<Map<string, CachedData<any>>>(new Map());
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Function declarations first
  const fetchCustomerProfile = useCallback(async (userId: string): Promise<CustomerProfile | null> => {
    try {
      const { data: profile, error } = await supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (!error && profile) {
        setCustomerProfile(profile);
        setCachedData(`customer_profile_${userId}`, profile);
        return profile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching customer profile:', error);
      return null;
    }
  }, []);

  const fetchBusinessProfile = useCallback(async (userId: string): Promise<BusinessProfile | null> => {
    try {
      // Use the comprehensive user profile service
      const profileResult = await getComprehensiveUserProfile(userId);

      if (profileResult.data?.businessProfile && !profileResult.error) {
        const profile = profileResult.data.businessProfile;
        setBusinessProfile(profile);
        setCachedData(`business_profile_${userId}`, profile);
        return profile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching business profile:', error);
      return null;
    }
  }, []);

  const initializeData = useCallback(async () => {
    setIsLoading(true);
    try {
      // First get user
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      setUser(authUser);

      // Try to fetch both customer and business profiles
      await Promise.allSettled([
        fetchCustomerProfile(authUser.id),
        fetchBusinessProfile(authUser.id)
      ]);
    } catch (error) {
      console.error('Error initializing data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchBusinessProfile, fetchCustomerProfile]);

  // Initialize data on mount
  useEffect(() => {
    if (!isInitialized) {
      initializeData();
      setIsInitialized(true);
    }
  }, [isInitialized, initializeData]);

  // Listen to auth changes from AuthContext (no duplicate auth listener)
  const { user: authUser, session } = useAuthContext();

  useEffect(() => {
    if (!session || !authUser) {
      // Clear all data when user signs out
      setUser(null);
      setCustomerProfile(null);
      setBusinessProfile(null);
      setDataCache(new Map());
      setIsLoading(false);
    } else if (authUser) {
      // Update user and fetch profiles when user signs in
      setUser(authUser);

      // Clear previous data first
      setCustomerProfile(null);
      setBusinessProfile(null);
      setDataCache(new Map());

      // Fetch new user's profiles in background without blocking UI
      Promise.allSettled([
        fetchCustomerProfile(authUser.id),
        fetchBusinessProfile(authUser.id)
      ]).catch(error => {
        console.error('Error fetching profiles after sign in:', error);
      });
    }
  }, [session, authUser, fetchCustomerProfile, fetchBusinessProfile]);

  const getUser = async (): Promise<User | null> => {
    if (user) return user;

    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (authUser) {
        setUser(authUser);
      }
      return authUser;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  };

  const getCustomerProfile = async (): Promise<CustomerProfile | null> => {
    if (customerProfile) return customerProfile;

    try {
      const authUser = await getUser();
      if (!authUser) return null;

      const profile = await fetchCustomerProfile(authUser.id);
      return profile;
    } catch (error) {
      console.error('Error getting customer profile:', error);
      return null;
    }
  };

  const getBusinessProfile = async (): Promise<BusinessProfile | null> => {
    if (businessProfile) return businessProfile;

    try {
      const authUser = await getUser();
      if (!authUser) return null;

      const profile = await fetchBusinessProfile(authUser.id);
      return profile;
    } catch (error) {
      console.error('Error getting business profile:', error);
      return null;
    }
  };

  const getCachedData = (key: string): any => {
    const cached = dataCache.get(key);
    if (!cached) return null;

    // Check if cache is still valid
    const now = Date.now();
    if (now - cached.lastFetched > CACHE_DURATION) {
      // Cache expired, remove it
      setDataCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(key);
        return newCache;
      });
      return null;
    }

    return cached.data;
  };

  const setCachedData = (key: string, data: any) => {
    setDataCache(prev => {
      const newCache = new Map(prev);
      newCache.set(key, {
        data,
        lastFetched: Date.now()
      });
      return newCache;
    });
  };

  const clearCache = () => {
    setDataCache(new Map());
    setUser(null);
    setCustomerProfile(null);
    setBusinessProfile(null);
  };

  const refreshUser = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      setUser(authUser);
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  const refreshCustomerProfile = async () => {
    try {
      const authUser = await getUser();
      if (!authUser) return;
      await fetchCustomerProfile(authUser.id);
    } catch (error) {
      console.error('Error refreshing customer profile:', error);
    }
  };

  const refreshBusinessProfile = async () => {
    try {
      const authUser = await getUser();
      if (!authUser) return;
      await fetchBusinessProfile(authUser.id);
    } catch (error) {
      console.error('Error refreshing business profile:', error);
    }
  };

  const refreshAll = async () => {
    await Promise.allSettled([
      refreshUser(),
      refreshCustomerProfile(),
      refreshBusinessProfile()
    ]);
  };

  const value: UserDataContextType = {
    user,
    isLoading,
    customerProfile,
    businessProfile,
    dataCache,
    getUser,
    getCustomerProfile,
    getBusinessProfile,
    getCachedData,
    setCachedData,
    clearCache,
    refreshUser,
    refreshCustomerProfile,
    refreshBusinessProfile,
    refreshAll,
  };

  return (
    <UserDataContext.Provider value={value}>
      {children}
    </UserDataContext.Provider>
  );
};

export const useUserData = (): UserDataContextType => {
  const context = useContext(UserDataContext);
  if (context === undefined) {
    throw new Error('useUserData must be used within a UserDataProvider');
  }
  return context;
};

// Convenience hooks for specific use cases
export const useAuth = () => {
  const { user, getUser, refreshUser, isLoading } = useUserData();
  return { user, getUser, refreshUser, isLoading };
};

export const useCustomerProfile = () => {
  const { customerProfile, getCustomerProfile, refreshCustomerProfile } = useUserData();
  return { customerProfile, getCustomerProfile, refreshCustomerProfile };
};

export const useBusinessProfile = () => {
  const { businessProfile, getBusinessProfile, refreshBusinessProfile } = useUserData();
  return { businessProfile, getBusinessProfile, refreshBusinessProfile };
};

export const useDataCache = () => {
  const { dataCache, getCachedData, setCachedData, clearCache } = useUserData();
  return { dataCache, getCachedData, setCachedData, clearCache };
};
