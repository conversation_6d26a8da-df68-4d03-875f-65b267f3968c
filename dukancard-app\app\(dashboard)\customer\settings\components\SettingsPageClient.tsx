import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Settings } from 'lucide-react-native';
import Animated, { FadeInUp } from 'react-native-reanimated';

import PasswordUpdateSection from "./PasswordUpdateSection";
import LinkEmailSection from "./LinkEmailSection";
import LinkPhoneSection from "./LinkPhoneSection";
import { useTheme } from '@/src/hooks/useTheme';
import { createSettingsPageStyles } from '@/styles/dashboard/customer/settings/settings';

interface SettingsPageClientProps {
  currentEmail: string | undefined;
  currentPhone: string | null | undefined;
  registrationType: 'google' | 'email' | 'phone';
}

export default function SettingsPageClient({
  currentEmail,
  currentPhone,
  registrationType,
}: SettingsPageClientProps) {
  const theme = useTheme();
  const styles = createSettingsPageStyles(theme);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Animated.View entering={FadeInUp.duration(400)} style={styles.cardContainer}>
        <View style={styles.card}>
          <View style={styles.content}>
            {/* Section Header */}
            <View style={styles.header}>
              <View style={styles.headerContent}>
                <View style={styles.iconContainer}>
                  <Settings size={20} color={theme.colors.primary} />
                </View>
                <Text style={styles.headerTitle}>
                  Account Settings
                </Text>
              </View>
            </View>

            {/* Settings Sections */}

            {/* Link Email - Show for all users with different behaviors */}
            <LinkEmailSection
              currentEmail={currentEmail}
              currentPhone={currentPhone}
              registrationType={registrationType}
            />

            {/* Link Phone - Show for all users with different behaviors */}
            <LinkPhoneSection
              currentEmail={currentEmail}
              currentPhone={currentPhone}
              registrationType={registrationType}
            />

            {/* Password Management - Not for Google users */}
            {registrationType !== 'google' && (
              <PasswordUpdateSection
                registrationType={registrationType}
              />
            )}

            <View style={styles.deleteProfileContainer}>
              <Text style={styles.deleteProfileText}>
                To delete your profile, please visit our website.
              </Text>
            </View>
          </View>
        </View>
      </Animated.View>
    </ScrollView>
  );
}
