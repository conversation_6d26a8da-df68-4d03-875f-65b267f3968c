import { supabase } from '@/lib/supabase';
import { BusinessProfile } from '@/backend/types/business';

export interface CustomerProfile {
  id: string;
  name?: string;
  phone?: string;
  email?: string;
  address?: string;
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  latitude?: number;
  longitude?: number;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ComprehensiveUserProfile {
  id: string;
  email?: string;
  phone?: string;
  full_name?: string;
  profile?: CustomerProfile;
  businessProfile?: BusinessProfile;
}

export class CustomerProfileService {
  static async updateCustomerName(userId: string, name: string) {
    return await supabase.auth.updateUser({
      data: { full_name: name }
    });
  }

  static async updateCustomerAddress(userId: string, addressData: any) {
    // Fetch coordinates from pincode table if available
    let latitude: number | null = null;
    let longitude: number | null = null;

    // GPS coordinates should be provided directly - no fallback to pincodes table
    // Coordinates will remain null if not provided via GPS

    return await supabase
      .from('customer_profiles')
      .update({
        address: addressData.address || null,
        pincode: addressData.pincode,
        city: addressData.city,
        state: addressData.state,
        locality: addressData.locality,
        latitude: latitude,
        longitude: longitude,
      })
      .eq('id', userId);
  }

  static async updateCustomerPhone(userId: string, phone: string) {
    const { error: updateError } = await supabase
      .from('customer_profiles')
      .update({ phone: phone })
      .eq('id', userId);

    if (updateError) {
      return { data: null, error: updateError };
    }

    // Update phone in Supabase auth.users table to maintain user ID consistency
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${phone}`,
    });

    return { data: null, error: authUpdateError };
  }

  static async updateCustomerEmail(email: string) {
    return await supabase.auth.updateUser({
      email: email || undefined,
    });
  }
}

export async function getCustomerProfile(userId: string) {
  return await supabase
    .from('customer_profiles')
    .select('*')
    .eq('id', userId)
    .single();
}

export async function checkCustomerProfileExists(userId: string) {
  const { data, error } = await supabase
    .from('customer_profiles')
    .select('id')
    .eq('id', userId)
    .single();

  return { exists: !!data && !error, error };
}

export async function createCustomerProfile(userId: string, profileData: Partial<CustomerProfile>) {
  return await supabase
    .from('customer_profiles')
    .insert({
      id: userId,
      ...profileData
    });
}

export async function updateCustomerProfile(userId: string, profileData: Partial<CustomerProfile>) {
  return await supabase
    .from('customer_profiles')
    .update(profileData)
    .eq('id', userId);
}

export async function getCustomerProfileById(userId: string) {
  return await getCustomerProfile(userId);
}

export async function getCustomerProfilesByIds(userIds: string[]) {
  return await supabase
    .from('customer_profiles')
    .select('*')
    .in('id', userIds);
}

export async function deleteCustomerProfile(userId: string) {
  return await supabase
    .from('customer_profiles')
    .delete()
    .eq('id', userId);
}

export async function getComprehensiveUserProfile(userId: string): Promise<{ data: ComprehensiveUserProfile | null; error: any }> {
  try {
    // Get user from auth
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { data: null, error: authError || 'User not found' };
    }

    // Get customer profile
    const { data: profile, error: profileError } = await getCustomerProfile(userId);

    const comprehensiveProfile: ComprehensiveUserProfile = {
      id: user.id,
      email: user.email,
      phone: user.phone,
      full_name: user.user_metadata?.full_name,
      profile: profile || undefined
    };

    return { data: comprehensiveProfile, error: profileError };
  } catch (error) {
    return { data: null, error };
  }
}