import { getCurrentUser } from '@/lib/auth/customerAuth';
import { supabase } from '@/lib/supabase';
import { compressImageModerateClient } from '@/src/utils/client-image-compression';
import { getCustomerAvatarPath } from '@/backend/supabase/utils/storage-paths';
import { deleteProfileImage, uploadProfileImage, extractFilePathFromUrl } from '@/backend/supabase/services/storage/storageService';
import * as ImagePicker from 'expo-image-picker';
import { ImageManipulator, SaveFormat } from 'expo-image-manipulator';
import { Alert } from 'react-native';

export interface AvatarUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface CameraPermissionResult {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

export interface MediaLibraryPermissionResult {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

/**
 * Request camera permission using the latest Expo method
 */
export async function requestCameraPermission(): Promise<CameraPermissionResult> {
  try {
    const { status, canAskAgain } = await ImagePicker.requestCameraPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status: status,
    };
  } catch (error) {
    console.error('Error requesting camera permission:', error);
    return {
      granted: false,
      canAskAgain: false,
      status: 'denied',
    };
  }
}

/**
 * Check camera permission status
 */
export async function checkCameraPermission(): Promise<CameraPermissionResult> {
  try {
    const { status, canAskAgain } = await ImagePicker.getCameraPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status,
    };
  } catch (error) {
    console.error('Error checking camera permission:', error);
    return {
      granted: false,
      canAskAgain: false,
      status: 'denied',
    };
  }
}

/**
 * Request media library permission
 */
export async function requestMediaLibraryPermission(): Promise<MediaLibraryPermissionResult> {
  try {
    const { status, canAskAgain } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status,
    };
  } catch (error) {
    console.error('Error requesting media library permission:', error);
    return {
      granted: false,
      canAskAgain: false,
      status: 'denied',
    };
  }
}

/**
 * Check media library permission status
 */
export async function checkMediaLibraryPermission(): Promise<MediaLibraryPermissionResult> {
  try {
    const { status, canAskAgain } = await ImagePicker.getMediaLibraryPermissionsAsync();

    return {
      granted: status === ImagePicker.PermissionStatus.GRANTED,
      canAskAgain,
      status,
    };
  } catch (error) {
    console.error('Error checking media library permission:', error);
    return {
      granted: false,
      canAskAgain: false,
      status: 'denied',
    };
  }
}

/**
 * Open camera to take a photo with advanced cropping
 */
export async function openCamera(): Promise<ImagePicker.ImagePickerResult> {
  // Check camera permission first
  const permission = await checkCameraPermission();

  if (!permission.granted) {
    const requestResult = await requestCameraPermission();
    if (!requestResult.granted) {
      throw new Error('Camera permission is required to take photos');
    }
  }

  return await ImagePicker.launchCameraAsync({
    mediaTypes: ['images'], // Updated to use array format instead of deprecated MediaTypeOptions
    allowsEditing: false, // Disable native editing - we'll process images ourselves
    quality: 1.0, // Higher quality for processing
    base64: false,
  });
}

/**
 * Open camera and return processed image
 */
export async function openCameraForAvatar(): Promise<ImagePicker.ImagePickerResult> {
  try {
    // Take a photo using expo-image-picker
    const result = await openCamera();
    return result;
  } catch (error) {
    console.error('Camera error:', error);
    throw error;
  }
}

/**
 * Open image picker to select from gallery
 */
export async function openImagePicker(): Promise<ImagePicker.ImagePickerResult> {
  // Check media library permission first
  const permission = await checkMediaLibraryPermission();

  if (!permission.granted) {
    const requestResult = await requestMediaLibraryPermission();
    if (!requestResult.granted) {
      throw new Error('Media library permission is required to select photos');
    }
  }

  return await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ['images'], // Updated to use array format instead of deprecated MediaTypeOptions
    allowsEditing: false, // Disable native editing - we'll process images ourselves
    quality: 1.0, // Higher quality for processing
    base64: false,
  });
}

/**
 * Open gallery and return processed image
 */
export async function openGalleryForAvatar(): Promise<ImagePicker.ImagePickerResult> {
  try {
    // Select an image from gallery
    const result = await openImagePicker();
    return result;
  } catch (error) {
    console.error('Gallery error:', error);
    throw error;
  }
}

/**
 * Show image picker options (Camera or Gallery) with permission-aware options
 */
export async function showImagePickerOptions(): Promise<'camera' | 'gallery' | null> {
  // Check camera permission status
  const cameraPermission = await checkCameraPermission();

  return new Promise((resolve) => {
    const options = [];

    // Only show camera option if permission is granted or can be requested
    if (cameraPermission.granted || cameraPermission.canAskAgain) {
      options.push({
        text: 'Camera',
        onPress: () => resolve('camera'),
      });
    }

    // Always show gallery option
    options.push({
      text: 'Gallery',
      onPress: () => resolve('gallery'),
    });

    options.push({
      text: 'Cancel',
      style: 'cancel' as const,
      onPress: () => resolve(null),
    });

    Alert.alert(
      'Select Avatar',
      cameraPermission.granted || cameraPermission.canAskAgain
        ? 'Choose how you want to set your profile picture'
        : 'Camera permission denied. You can select from gallery.',
      options,
      { cancelable: true, onDismiss: () => resolve(null) }
    );
  });
}

/**
 * Compress image for avatar upload using client-side compression
 * Targets under 100KB to match web version requirements
 */
async function compressImage(uri: string): Promise<{ uri: string; size: number }> {
  try {
    // Get original file size
    const response = await fetch(uri);
    const blob = await response.blob();
    const originalSize = blob.size;

    // Use aggressive compression for avatars to match web version (<100KB)
    const compressionResult = await compressImageModerateClient(uri, originalSize, {
      targetSizeKB: 95, // Target under 100KB like web version
      maxDimension: 400, // 400px max dimension for avatars
      quality: 0.8,
      format: 'webp'
    });

    return {
      uri: compressionResult.uri || uri, // Fallback to original URI if compression result doesn't have uri
      size: compressionResult.finalSizeKB * 1024 // Convert back to bytes
    };
  } catch (error) {
    console.error('Error compressing image:', error);
    // Fallback to original URI with estimated size
    const response = await fetch(uri);
    const blob = await response.blob();
    return {
      uri,
      size: blob.size
    };
  }
}

/**
 * Upload avatar image to Supabase storage
 */
export async function uploadAvatarImage(imageUri: string, userId?: string): Promise<AvatarUploadResult> {
  try {
    let user;
    if (userId) {
      user = { id: userId };
    } else {
      user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }
    }

    // Compress image
    const compressedResult = await compressImage(imageUri);

    // For React Native, we need to handle local file URIs carefully
    // since fetch() might not be able to access them directly
    let blob: Blob;

    try {
      // Try to read the compressed file
      const response = await fetch(compressedResult.uri);
      blob = await response.blob();
    } catch (fetchError) {
      console.log('Compressed file fetch failed, trying original image:', fetchError);

      // Fallback: Use the original imageUri
      try {
        const originalResponse = await fetch(imageUri);
        blob = await originalResponse.blob();
      } catch (originalError) {
        console.error('Both fetch attempts failed:', originalError);
        return {
          success: false,
          error: 'Failed to read image file. Please try a different image.',
        };
      }
    }

    // Generate filename
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const filename = `avatar_${timestamp}.webp`;

    // Upload using direct storage service
    const uploadResult = await uploadProfileImage(
      blob,
      'customer',
      filename,
      { contentType: 'image/webp' }
    );

    if (!uploadResult.success) {
      console.error('Avatar upload error:', uploadResult.error);
      return {
        success: false,
        error: uploadResult.error || 'Failed to upload avatar',
      };
    }

    const uploadData = uploadResult.data;
    if (!uploadData?.publicUrl) {
      return {
        success: false,
        error: 'Could not retrieve public URL after upload',
      };
    }

    return { success: true, url: uploadData.publicUrl };
  } catch (error) {
    console.error('Error uploading avatar:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload avatar',
    };
  }
}

/**
 * Update customer profile with new avatar URL
 */
export async function updateCustomerAvatarUrl(avatarUrl: string | null): Promise<AvatarUploadResult> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error } = await supabase
      .from('customer_profiles')
      .update({
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (error) {
      console.error('Error updating avatar URL:', error);
      return {
        success: false,
        error: `Failed to update avatar URL: ${error.message}`,
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating customer avatar URL:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update avatar URL',
    };
  }
}

/**
 * Delete customer avatar from Supabase storage and update profile
 */
export async function deleteCustomerAvatar(avatarUrl: string): Promise<AvatarUploadResult> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Extract file path from the public URL
    const filePath = extractFilePathFromUrl(avatarUrl);
    if (!filePath) {
      return { success: false, error: 'Invalid avatar URL provided' };
    }

    // Delete the image from storage
    const deleteResult = await deleteProfileImage('customer', filePath);
    if (!deleteResult.success) {
      console.error('Error deleting avatar from storage:', deleteResult.error);
      return { success: false, error: deleteResult.error || 'Failed to delete avatar from storage' };
    }

    // Update the customer profile to remove the avatar URL
    const updateResult = await updateCustomerAvatarUrl(null);
    if (!updateResult.success) {
      console.error('Error updating customer profile after avatar deletion:', updateResult.error);
      return { success: false, error: updateResult.error || 'Failed to update profile after avatar deletion' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in deleteCustomerAvatar:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete avatar',
    };
  }
}

